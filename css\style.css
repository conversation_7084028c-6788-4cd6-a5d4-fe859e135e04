/* Base Styles */
:root {
  --primary-color: #a18cd1; /* Soft purple */
  --secondary-color: #fbc2eb; /* Soft pink */
  --dark-color: #212529;
  --light-color: #fff;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --text-color: #333;
  --bg-color: #fff;
  --card-bg: #fff;
  --border-color: #e9ecef;
  --shadow-color: rgba(161, 140, 209, 0.1);
  --transition: all 0.3s ease;
  --section-padding: 80px 0;
  --header-bg: #fff;
  --footer-bg: #9178c9; /* Softer purple that matches the theme */
  --footer-text: #fff;
  --nav-link-hover: #fbc2eb;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Poppins", sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: var(--transition);
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--primary-color);
  letter-spacing: 1px;
  animation: fadeIn 1.2s;
}

.underline {
  height: 3px;
  width: 60px;
  background: var(--secondary-color);
  margin: 0 auto;
  border-radius: 2px;
  margin-bottom: 8px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  border: none;
}

.primary-btn {
  background: var(--primary-color);
  color: #fff;
}

.primary-btn:hover {
  background: #3a5bef;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(74, 107, 255, 0.2);
}

.secondary-btn {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.secondary-btn:hover {
  background: var(--primary-color);
  color: #fff;
  transform: translateY(-3px);
}

.small-btn {
  padding: 8px 15px;
  font-size: 0.9rem;
}

/* Header Styles */
header {
  display: none;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.logo {
  display: flex;
  align-items: center;
  position: relative;
}

.logo img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 4px 15px rgba(161, 140, 209, 0.3);
  transition: all 0.3s ease;
  border: 3px solid #a18cd1;
}

.logo img:hover {
  border-color: #fbc2eb;
  box-shadow: 0 6px 20px rgba(161, 140, 209, 0.5);
}

/* Remove all previous animations */
.logo::before,
.logo::after {
  display: none;
}

@media (max-width: 768px) {
  .logo img {
    width: 55px;
    height: 55px;
  }
}

.nav-links {
  display: flex;
  align-items: center;
}

.nav-links li {
  margin: 0 15px;
}

.nav-links a {
  color: var(--text-color);
  font-weight: 500;
  position: relative;
  padding: 6px 12px;
  border-radius: 8px;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}

.nav-links a:hover,
.nav-links a.active {
  background: var(--nav-link-hover);
  color: #fff;
  box-shadow: 0 2px 8px rgba(67, 176, 241, 0.1);
}

.nav-links a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.nav-links a:hover::after,
.nav-links a.active::after {
  width: 100%;
}

.theme-toggle {
  margin-left: 20px;
  cursor: pointer;
  font-size: 1.3rem;
  color: var(--text-color);
  transition: color 0.2s;
}

.theme-toggle:hover {
  color: var(--nav-link-hover);
}

.hamburger {
  display: none;
  cursor: pointer;
}

.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px 0;
  background-color: var(--text-color);
  transition: var(--transition);
}

/* Hero Section */
.hero {
  padding-top: 120px;
  padding-bottom: 80px;
  background-color: var(--bg-color);
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 50px;
}

.hero-text {
  flex: 1;
}

.hero-text h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--text-color);
}

.hero-text h1 span {
  color: var(--primary-color);
}

.hero-text h2 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 20px;
  color: var(--secondary-color);
}

.hero-text p {
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.hero-btns {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--light-color);
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: var(--transition);
}

.social-icons a:hover {
  background-color: var(--primary-color);
  color: #fff;
  transform: translateY(-3px);
}

.hero-image {
  flex: 1;
  text-align: center;
}

.hero-image img {
  max-width: 80%;
  border-radius: 20px;
  box-shadow: 0 20px 40px var(--shadow-color);
  transition: var(--transition);
}

.hero-image img:hover {
  transform: scale(1.02);
}

/* About Section */
.about {
  padding: 60px 0;
  margin: 0;
  background-color: var(--bg-color);
}

.about-content {
  display: flex;
  justify-content: center;
}

.about-text {
  max-width: 800px;
}

.about-text p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  text-align: justify;
}

.personal-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-title {
  font-weight: 600;
  color: var(--primary-color);
}

.info-value {
  color: var(--text-color);
}

/* Skills Section */
.skills {
  padding: 60px 0;
  margin: 0;
  background-color: var(--light-color);
}

.skills-content {
  display: flex;
  flex-wrap: wrap;
  gap: 50px;
}

.technical-skills,
.soft-skills {
  flex: 1;
  min-width: 300px;
}

.technical-skills h3,
.soft-skills h3 {
  font-size: 1.5rem;
  margin-bottom: 30px;
  text-align: center;
  color: var(--primary-color);
}

.skill-items {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.skill-item {
  width: 100%;
}

.skill-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.skill-info p {
  font-weight: 500;
}

.skill-bar {
  height: 10px;
  background-color: var(--border-color);
  border-radius: 10px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 10px;
  transition: width 1.5s ease-in-out;
}

.skill-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 20px;
}

.skill-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: var(--transition);
}

.skill-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px var(--shadow-color);
}

.skill-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.skill-card h4 {
  font-size: 1rem;
  font-weight: 500;
}

/* Projects Section */
.projects {
  padding: 60px 0;
  margin: 0;
  background-color: var(--bg-color);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  margin-top: 32px;
  justify-items: center;
}

.project-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px var(--shadow-color);
  border: 1px solid var(--border-color);
  padding: 28px 22px 22px 22px;
  min-height: 220px;
  width: 100%;
  max-width: 370px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  transition: box-shadow 0.3s, transform 0.3s, border-color 0.3s;
  animation: fadeInUp 1s;
}
.project-card:hover {
  box-shadow: 0 8px 32px var(--shadow-color);
  border-color: var(--primary-color);
  transform: translateY(-8px) scale(1.025);
}
.project-info h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10px;
  letter-spacing: 0.01em;
  transition: color 0.2s;
}
.project-card:hover .project-info h3 {
  color: var(--secondary-color);
}
.project-info p {
  font-size: 1rem;
  color: #444;
  margin-bottom: 18px;
  line-height: 1.5;
}
.project-links {
  margin-top: auto;
}
.project-links .btn.small-btn {
  padding: 7px 18px;
  font-size: 0.98rem;
  border-radius: 8px;
  background: var(--secondary-color);
  color: #fff;
  border: none;
  font-weight: 500;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px var(--shadow-color);
}
.project-links .btn.small-btn:hover {
  background: var(--primary-color);
  color: #fff;
  box-shadow: 0 4px 16px var(--shadow-color);
}

/* Fade-in animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.project-note {
  margin-top: 24px;
  text-align: center;
  font-size: 1.08rem;
  color: #90c6e0;
  background: #f8fbff;
  border-radius: 8px;
  padding: 14px 18px;
  box-shadow: 0 2px 8px rgba(120, 144, 156, 0.07);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 900px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 22px;
  }
  .project-card {
    max-width: 100%;
    padding: 20px 10px 16px 10px;
  }
}

@media (max-width: 600px) {
  .projects {
    padding: 40px 0 20px 0;
  }
  .section-header h2 {
    font-size: 1.6rem;
  }
  .project-card {
    padding: 14px 4px 12px 4px;
    min-height: 160px;
    font-size: 0.98rem;
  }
  .project-info h3 {
    font-size: 1.08rem;
  }
  .project-info p {
    font-size: 0.93rem;
  }
  .project-links .btn.small-btn {
    font-size: 1.05rem;
    padding: 10px 18px;
    margin-top: 8px;
    width: 100%;
    box-sizing: border-box;
  }
  .project-note {
    font-size: 0.98rem;
    padding: 12px 6px;
  }
}

/* Education Section */
.education {
  padding: 60px 0;
  margin: 0;
  background-color: var(--bg-color);
}

.education-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 40px;
}

.education-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px var(--shadow-color);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid var(--border-color);
}

.education-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px var(--shadow-color);
}

.education-header {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: var(--light-color);
  border-bottom: 1px solid var(--border-color);
}

.education-logo {
  width: 60px;
  height: 60px;
  margin-right: 15px;
  background-color: white;
  border-radius: 8px;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.education-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: var(--transition);
}

.education-card:hover .education-logo img {
  transform: scale(1.05);
}

.education-title {
  flex: 1;
}

.education-title h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.education-title h4 {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
}

.education-content {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.education-info {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.education-info p {
  font-size: 0.9rem;
  color: var(--secondary-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.education-info p i {
  color: var(--primary-color);
  font-size: 1rem;
}

.education-content .description {
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-color);
  margin-bottom: 15px;
}

.college-link {
  margin-top: auto;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  padding-top: 10px;
}

.college-link:hover {
  color: var(--secondary-color);
  transform: translateX(5px);
}

.college-link i {
  font-size: 0.8rem;
}

/* Responsive styles for education section */
@media (max-width: 768px) {
  .education-grid {
    grid-template-columns: 1fr;
  }

  .education-card {
    max-width: 500px;
    margin: 0 auto;
  }

  .education-header {
    padding: 15px;
  }

  .education-logo {
    width: 50px;
    height: 50px;
  }
}

/* Certificates Section */
.certificates {
  background-color: #fff;
  padding: 60px 0;
}

.certificates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 30px;
}

.certificate-link {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

.certificate-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 32px 18px 24px 18px;
  min-height: 210px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: var(--transition);
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
}

.certificate-card i {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.certificate-card h3 {
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  margin: 0;
}

.certificate-card:hover {
  box-shadow: 0 15px 30px var(--shadow-color);
  transform: translateY(-10px) scale(1.03);
  cursor: pointer;
}

@media (max-width: 700px) {
  .certificates-grid {
    grid-template-columns: 1fr;
  }
  .certificate-card {
    min-height: 160px;
    padding: 20px 10px 16px 10px;
    max-width: 100%;
  }
}

/* Contact Section */
.contact {
  background-color: #fff;
  min-height: auto;
  padding: 60px 0;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  align-items: center;
  position: relative;
}

.contact-content::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 80%;
  background-color: #a18cd1;
}

.contact-left {
  padding-right: 4rem;
}

.contact-right {
  display: flex;
  justify-content: center;
  align-items: center;
}

.programmer-illustration {
  width: 100%;
  max-width: 400px;
  height: auto;
}

.contact-right img {
  width: 100%;
  height: auto;
}

.contact-left h2 {
  font-size: 2.5rem;
  color: #a18cd1;
  margin-bottom: 2rem;
  font-weight: 700;
}

.contact-info {
  background-color: #a18cd1;
  padding: 2rem;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #fff;
}

.contact-item i {
  font-size: 1.2rem;
  color: #fff;
  width: 20px;
}

.contact-item p {
  font-size: 1rem;
  margin: 0;
  color: #fff;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  border: 2px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #fff;
  color: #a18cd1;
  transform: translateY(-3px);
}

.direct-message-btn {
  display: inline-block;
  background: #fff;
  color: #a18cd1;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  margin-top: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.direct-message-btn:hover {
  background: #fbc2eb;
  color: #fff;
  transform: translateY(-3px);
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-content::after {
    display: none;
  }

  .contact-left {
    padding-right: 0;
  }

  .contact-right {
    padding-left: 0;
  }

  .contact-left h2 {
    font-size: 2rem;
    text-align: center;
  }

  .programmer-illustration {
    max-width: 250px;
    margin: 0 auto;
  }
}

/* Feedback Section with Tom and Jerry */
.feedback {
  padding: 60px 0;
  background-color: transparent;
  margin: 0;
  position: relative;
  width: 100%;
  overflow: hidden;
}

.feedback-container {
  max-width: 800px;
  margin: 0 auto;
}

.feedback-content {
  text-align: center;
  background-color: #fff;
  padding: 50px;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.tom-image {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100px;
  height: auto;
  z-index: 1;
}

.jerry-image {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 80px;
  height: auto;
  z-index: 1;
}

.feedback-content h2 {
  color: var(--primary-color);
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.feedback-content p {
  color: var(--text-color);
  font-size: 1.2rem;
  margin-bottom: 30px;
  position: relative;
  z-index: 2;
}

.feedback-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 14px 34px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 30px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  border: 2px solid transparent;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.feedback-btn:hover {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}


@media (max-width: 480px) {
  .feedback-content {
    padding: 40px 20px;
  }

  .tom-image {
    width: 50px;
    top: 5px;
    left: 5px;
  }

  .jerry-image {
    width: 40px;
    bottom: 5px;
    right: 5px;
  }

  .feedback-content h2 {
    font-size: 1.8rem;
  }

  .feedback-content p {
    font-size: 1rem;
  }
}

/* Footer - Empty as requested */
footer {
  padding: 20px 0;
  background: var(--footer-bg);
  color: var(--footer-text);
  text-align: center;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  border-bottom: none;
  margin-bottom: 0;
  margin-top: 0;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 45px;
  height: 45px;
  background-color: #a18cd1;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  z-index: 999;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-to-top.active {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #fbc2eb;
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Section backgrounds */
.about,
.skills,
.projects,
.education,
.certificates,
.contact {
  background-color: var(--bg-color) !important;
}

.dark-mode .education-title h3,
.dark-mode .education-title h4,
.dark-mode .certificate-card h3 {
  color: #fff !important;
}

.project-note {
  margin-top: 24px;
  text-align: center;
  font-size: 1.08rem;
  color: #90c6e0;
  background: #f8fbff;
  border-radius: 8px;
  padding: 14px 18px;
  box-shadow: 0 2px 8px rgba(120, 144, 156, 0.07);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.dark-mode .project-note {
  background: #181a1b;
  color: #b3e0ef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
}

/* Animations */
@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.fade-in {
  opacity: 0;
  animation: fadeIn 1s forwards;
}
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}
.slide-in {
  opacity: 0;
  transform: translateY(40px);
  animation: slideIn 1s forwards;
}
@keyframes slideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Section fade/slide on scroll (add .fade-in or .slide-in to section in HTML) */
section.fade-in,
section.slide-in {
  will-change: opacity, transform;
}

/* Card and Button Hover Effects */
.btn,
.project-card,
.education-card,
.certificate-card {
  transition: box-shadow 0.25s, transform 0.25s, background 0.2s, color 0.2s;
}
.btn:hover {
  background: var(--secondary-color);
  color: #fff;
  box-shadow: 0 4px 16px var(--shadow-color);
  transform: translateY(-2px) scale(1.03);
}
.project-card:hover,
.education-card:hover,
.certificate-card:hover {
  box-shadow: 0 8px 24px var(--shadow-color);
  transform: translateY(-4px) scale(1.02);
}

/* Subtle Dividers and Underlines */
.underline {
  height: 3px;
  width: 60px;
  background: var(--secondary-color);
  margin: 0 auto;
  border-radius: 2px;
  margin-bottom: 8px;
}

/* Improved Spacing and Card Shadows */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Section headers */
.section-header h2 {
  font-size: 2.5rem;
  color: #a18cd1;
  margin-bottom: 1rem;
}

.section-header .underline {
  background: #a18cd1;
  width: 80px;
  height: 3px;
  margin-bottom: 2rem;
}

.contact-info {
  background: var(--primary-color);
  color: #fff;
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow: 0 4px 16px var(--shadow-color);
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.contact-info .contact-item i {
  color: #fff;
}
.contact-info h3,
.contact-info p {
  color: #fff;
}
.contact-info .social-links a {
  background: #fff;
  color: var(--primary-color);
  border-radius: 50%;
  margin-right: 8px;
  transition: background 0.2s, color 0.2s;
}
.contact-info .social-links a:hover {
  background: var(--secondary-color);
  color: #fff;
}
@media (max-width: 900px) {
  .contact-content {
    flex-direction: column;
  }
  .contact-info {
    margin-bottom: 24px;
  }
}

@media (max-width: 900px) {
  header .container {
    flex-direction: row;
    padding: 14px 8px;
  }
  .logo img {
    width: 40px;
    height: 40px;
  }
  .nav-links {
    gap: 8px;
  }
  .education-logo {
    max-width: 60px;
    max-height: 60px;
    padding: 4px;
  }
}

@media (max-width: 600px) {
  header .container {
    flex-direction: row;
    padding: 10px 4px;
  }
  .logo img {
    width: 35px;
    height: 35px;
  }
  .nav-links {
    gap: 4px;
    font-size: 0.98rem;
  }
  .education-logo {
    max-width: 44px;
    max-height: 44px;
    padding: 2px;
  }
  .education-logo img {
    max-width: 100%;
    max-height: 100%;
    height: auto;
    object-fit: contain;
  }
}

.instagram-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white !important;
  border: none;
  transition: transform 0.3s, box-shadow 0.3s;
}

.instagram-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(220, 39, 67, 0.3);
  background: linear-gradient(45deg, #e6683c 0%, #dc2743 50%, #cc2366 100%);
}

.instagram-btn i {
  font-size: 1.2em;
}

/* Sidebar Navigation */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: #0d1117;
  padding: 40px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.sidebar-header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.profile-img {
  width: 130px;
  height: 130px;
  border-radius: 50%;
  margin: 0 auto;
  padding: 5px;
  background: linear-gradient(145deg, #a18cd1, #fbc2eb);
  box-shadow: 0 0 20px rgba(161, 140, 209, 0.3);
  object-fit: cover;
  border: 3px solid #1a1d24;
  display: block;
  aspect-ratio: 1 / 1; /* Ensure perfect circle */
}

/* Remove hover effect */
.profile-img:hover {
  transform: none;
  box-shadow: 0 0 20px rgba(161, 140, 209, 0.3);
}

.profile-name {
  color: #fff;
  font-size: 26px;
  font-weight: 600;
  margin: 15px 0;
  text-align: center;
}

.nav-menu {
  margin-top: 30px;
  padding: 0;
  list-style: none;
}

.nav-menu li {
  margin-bottom: 8px;
}

.nav-menu a {
  color: #8b949e;
  text-decoration: none;
  font-size: 15px;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-menu a i {
  margin-right: 12px;
  font-size: 18px;
  opacity: 0.8;
  width: 20px;
  text-align: center;
}

.nav-menu a:hover,
.nav-menu a.active {
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  transform: translateX(5px);
}

.nav-menu a.active {
  background: rgba(161, 140, 209, 0.3);
  color: #fff;
  position: relative;
  padding-left: 20px;
  font-weight: 600;
}

.nav-menu a.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  border-radius: 0 2px 2px 0;
}

.nav-menu a.active i {
  opacity: 1;
  color: var(--secondary-color);
}

.social-icons-bottom {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: auto;
  padding: 20px 0;
}

.social-icons-bottom a {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8b949e;
  font-size: 16px;
  transition: all 0.3s ease;
}

.social-icons-bottom a:hover {
  background: rgba(161, 140, 209, 0.15);
  color: #a18cd1;
  transform: translateY(-3px);
}

.copyright-text {
  color: #8b949e;
  font-size: 13px;
  margin-top: 15px;
  opacity: 0.8;
}


/* Main content adjustment */
.main-content {
  margin-left: 280px;
  padding: 40px;
  min-height: 100vh;
  background: #fff;
}

@media (max-width: 991px) {
  .main-content {
    margin-left: 0;
    padding: 20px;
  }

  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }
}

/* Mobile Header */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
  padding: 10px 0;
}

.mobile-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobile-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mobile-profile-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-color);
}

.mobile-logo h1 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
}

.mobile-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--primary-color);
  cursor: pointer;
}

@media (max-width: 991px) {
  .mobile-header {
    display: block;
  }

  .main-content {
    padding-top: 70px;
  }
}

/* Update Technical Skills section with icons */
.skill-name-with-icon {
  display: flex;
  align-items: center;
  gap: 10px;
}

.skill-name-with-icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
  width: 30px;
  text-align: center;
}

.skill-name-with-icon p {
  font-weight: 500;
  margin: 0;
}

.skill-item {
  transition: transform 0.3s ease;
}

.skill-item:hover {
  transform: translateX(5px);
}

.skill-bar {
  height: 8px;
  background-color: var(--border-color);
  border-radius: 10px;
  overflow: hidden;
  margin-top: 8px;
}

/* Ensure consistent styling for soft skills */
.skill-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 20px;
}

.skill-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.skill-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.skill-card h4 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

/* Ensure all skill cards have the same hover effect */
.skill-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow-color);
}

/* Make both technical and soft skills consistent */
.technical-skills .skill-cards,
.soft-skills .skill-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 20px;
}

.technical-skills .skill-card,
.soft-skills .skill-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.technical-skills .skill-card i,
.soft-skills .skill-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.technical-skills .skill-card h4,
.soft-skills .skill-card h4 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.technical-skills .skill-card:hover,
.soft-skills .skill-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow-color);
}
